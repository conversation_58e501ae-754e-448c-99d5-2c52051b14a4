package com.ue.platform.service.seatAssistant;

import com.alibaba.fastjson.JSONObject;
import com.ue.common.exception.BizExceptionCode;
import com.ue.commondata.utils.JSONUtil;
import com.ue.frm.i18n.response.BizException;
import com.ue.frm.util.UeDateTimeUtil;
import com.ue.frm.util.UeUtil;
import com.ue.platform.dao.processNavigation.FlowDAO;
import com.ue.platform.dao.processNavigation.FlowEdgeDAO;
import com.ue.platform.dao.processNavigation.FlowIntentionDAO;
import com.ue.platform.dao.processNavigation.FlowNodeDAO;
import com.ue.platform.domain.entity.processNavigation.*;

import com.ue.platform.domain.dto.processNavigation.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 流程图服务实现（通话场景，参考机器人逻辑但适配多对一关系）
 */
@Service
@Slf4j
public class FlowGraphServiceImpl implements FlowGraphService {

    @Autowired
    private FlowDAO flowDAO;

    @Autowired
    private FlowNodeDAO flowNodeDAO;

    @Autowired
    private FlowEdgeDAO flowEdgeDAO;

    @Autowired
    private FlowIntentionDAO flowIntentionDAO;

//    @Autowired
//    private TransactionTemplate transactionTemplate;

    @Autowired
    private IntentionService intentionService;

    @Override
    public void saveOrUpdate(FlowGraphDTO dto, String accountId) {
        Long flowId = dto.getFlowId();

        // 1. 检查流程是否存在
        Flow flow = flowDAO.findById(flowId, accountId);
        if (Objects.isNull(flow)) {
            throw BizException.of(BizExceptionCode.DATA_NOT_FOUND);
        }

        // 2. 校验节点数据
        List<FlowNodeDto> nodeDTOList = dto.getNodes();
        if (CollectionUtils.isEmpty(nodeDTOList)) {
            throw BizException.of(BizExceptionCode.PARAM_ERROR);
        }

        List<FlowEdgeDto> nodeEdgeList = dto.getEdges();

        // 3. 处理节点数据（通话场景简化）
        List<FlowNode> nodeList = nodeDTOList.stream().map(v -> {
            // 直接转换DTO到PO
            FlowNode nodePO = toNodePO(v);
            nodePO.setZIndex(v.getZIndex());

            // 从data字段中解析具体数据
            JSONObject data = v.getData();
            FlowNodeDataDto bean = data.toJavaObject(FlowNodeDataDto.class);

            nodePO.setName(bean.getName());
            nodePO.setType(bean.getType());
            nodePO.setFunctionType(bean.getFunctionType());
            nodePO.setGuide(bean.getGuide());

            nodePO.setFlowId(flowId);
            nodePO.setAccountId(accountId);
            return nodePO;
        }).collect(Collectors.toList());

        // 4. 处理边数据
        List<FlowEdge> edgeList = nodeEdgeList.stream().map(v -> {
            FlowEdge edgePO = toEdgePO(v);
            edgePO.setFlowId(flowId);
            edgePO.setAccountId(accountId);
            return edgePO;
        }).collect(Collectors.toList());

        // 5. 处理意图绑定（直接使用DTO中的意图ID列表）
        List<Long> bindIntentionIds = dto.getIntentionIds();
        if (!CollectionUtils.isEmpty(bindIntentionIds)) {
            // 检查意图独占性
            checkIntentionConflicts(accountId, flowId, bindIntentionIds);
        }

        // 6. 事务性保存
//        transactionTemplate.executeWithoutResult(t -> {
            // 硬删除旧数据（减少冗余数据）
            flowNodeDAO.hardDeleteByFlowId(accountId, flowId);
            flowEdgeDAO.hardDeleteByFlowId(accountId, flowId);

            // 批量插入新数据
            if (!CollectionUtils.isEmpty(nodeList)) {
                flowNodeDAO.saveBatch(nodeList, accountId);
            }
            if (!CollectionUtils.isEmpty(edgeList)) {·
                flowEdgeDAO.saveBatch(edgeList, accountId);
            }

            // 硬删除旧的意图绑定关系，然后批量插入新的
            flowIntentionDAO.hardDeleteByFlowId(accountId, flowId);
            if (!CollectionUtils.isEmpty(bindIntentionIds)) {
                List<FlowIntention> flowIntentions = buildFlowIntentions(accountId, flowId, flow.getName(), bindIntentionIds);
                flowIntentionDAO.saveBatch(flowIntentions, accountId);
            }
//        });

        log.info("流程保存成功，flowId: {}, nodeCount: {}, edgeCount: {}, intentionCount: {}",
                flowId, nodeList.size(), edgeList.size(), 
                bindIntentionIds != null ? bindIntentionIds.size() : 0);
    }

    @Override
    public FlowGraphDTO queryByFlowId(Long flowId, String accountId) {
        // 1. 查询流程基础信息
        Flow flow = flowDAO.findById(flowId, accountId);
        if (flow == null) {
            throw BizException.of(BizExceptionCode.DATA_NOT_FOUND);
        }

        // 2. 查询节点和边
        List<FlowNode> nodes = flowNodeDAO.queryByFlowId(accountId, flowId);
        List<FlowEdge> edges = flowEdgeDAO.queryByFlowId(accountId, flowId);

        // 3. 查询绑定的意图
        ArrayList<Long> flows = new ArrayList<>();
        flows.add(flowId);
        List<FlowIntention> flowIntentions = flowIntentionDAO.queryByFlowIds(accountId, flows);
        List<Long> intentionIds = flowIntentions.stream()
                .map(FlowIntention::getIntentionId)
                .collect(Collectors.toList());

        // 4. 转换为DTO
        List<FlowNodeDto> nodeDtos = nodes.stream()
                .map(this::toNodeDTO)
                .collect(Collectors.toList());

        List<FlowEdgeDto> edgeDtos = edges.stream()
                .map(this::toEdgeDTO)
                .collect(Collectors.toList());

        return FlowGraphDTO.builder()
                .name(flow.getName())
                .flowId(flowId)
                .nodes(nodeDtos)
                .edges(edgeDtos)
                .intentionIds(intentionIds)
                .build();
    }

    /**
     * DTO转PO
     */
    private FlowNode toNodePO(FlowNodeDto dto) {
        if (dto == null) {
            return null;
        }

        return FlowNode.builder()
                .id(dto.getId())
                .shape(dto.getShape())
                .view(dto.getView())

                // JSON字段转换为String存储（保持与机器人一致）
                .data(JSONUtil.toJsonStr(dto.getData()))
                .position(JSONUtil.toJsonStr(dto.getPosition()))
                .ports(JSONUtil.toJsonStr(dto.getPorts()))
                .size(JSONUtil.toJsonStr(dto.getSize()))
                .build();
    }

    /**
     * PO转DTO(页面数据回显)
     */
    private FlowNodeDto toNodeDTO(FlowNode po) {
        if (po == null) {
            return null;
        }

        FlowNodeDto dto = new FlowNodeDto();
        dto.setId(po.getId());
        dto.setShape(po.getShape());
        dto.setView(po.getView());
        dto.setZIndex(po.getZIndex());

        // String转换回JSON对象
        dto.setData(strToJsonObj(po.getData()));
        dto.setPosition(strToJsonObj(po.getPosition()));
        dto.setPorts(strToJsonObj(po.getPorts()));
        dto.setSize(strToJsonObj(po.getSize()));

        return dto;
    }

    /**
     * DTO转PO
     */
    private FlowEdge toEdgePO(FlowEdgeDto dto) {
        if (dto == null) {
            return null;
        }

        FlowEdge edgePO = new FlowEdge();
        edgePO.setId(dto.getId());

        // 处理源和目标节点信息
        if (dto.getSource() != null) {
            edgePO.setSourceCell(dto.getSource().getCell());
            edgePO.setSourcePort(dto.getSource().getPort());
        }
        if (dto.getTarget() != null) {
            edgePO.setTargetCell(dto.getTarget().getCell());
            edgePO.setTargetPort(dto.getTarget().getPort());
        }
        if (dto.getData() != null) {
            edgePO.setName(dto.getData().getName());
        }

        // JSON字段转换为String存储
        edgePO.setData(JSONUtil.toJsonStr(dto.getData()));
        edgePO.setLabels(JSONUtil.toJsonStr(dto.getLabels()));

        return edgePO;
    }

    /**
     * PO转DTO(页面回显)
     */
    private FlowEdgeDto toEdgeDTO(FlowEdge po) {
        if (po == null) {
            return null;
        }

        FlowEdgeDto dto = new FlowEdgeDto();
        dto.setId(po.getId());

        // 创建源和目标点
        if (po.getSourceCell() != null) {
            dto.setSource(FlowPointDto.builder()
                    .cell(po.getSourceCell())
                    .port(po.getSourcePort())
                    .build());
        }
        if (po.getTargetCell() != null) {
            dto.setTarget(FlowPointDto.builder()
                    .cell(po.getTargetCell())
                    .port(po.getTargetPort())
                    .build());
        }

        // String转换回JSON对象
        dto.setData(strToConditionData(po.getData()));
        dto.setLabels(strToJsonArr(po.getLabels()));

        return dto;
    }

    private JSONObject strToJsonObj(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return JSONObject.parseObject(str);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", str, e);
            return null;
        }
    }

    private com.alibaba.fastjson.JSONArray strToJsonArr(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return com.alibaba.fastjson.JSONArray.parseArray(str);
        } catch (Exception e) {
            log.warn("JSONArray解析失败: {}", str, e);
            return null;
        }
    }

    private FlowConditionDataDto strToConditionData(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return JSONObject.parseObject(str, FlowConditionDataDto.class);
        } catch (Exception e) {
            log.warn("FlowConditionDataDto解析失败: {}", str, e);
            return null;
        }
    }

    /**
     * 构建FlowIntention对象列表
     */
    private List<FlowIntention> buildFlowIntentions(String accountId, Long flowId, String flowName, List<Long> intentionIds) {
        List<FlowIntention> flowIntentions = new ArrayList<>();
        String currentTime = UeDateTimeUtil.getCurrentDateTime();

        for (Long intentionId : intentionIds) {
            // 通过意图服务获取真实的意图名称
            String intentionName = getIntentionName(accountId, intentionId);

            FlowIntention flowIntention = FlowIntention.builder()
                    .id(Long.valueOf(UeUtil.snowId()))
                    .flowId(flowId)
                    .intentionId(intentionId)
                    //意图名称
                    .intentionName(intentionName)
                    //流程名称
                    .flowName(flowName)
                    .accountId(accountId)
                    .createTime(currentTime)
                    .updateTime(currentTime)
                    .isDelete(0)
                    .build();

            flowIntentions.add(flowIntention);
        }

        return flowIntentions;
    }

    /**
     * 检查意图冲突（参考机器人逻辑）
     */
    private void checkIntentionConflicts(String accountId, Long currentFlowId, List<Long> intentionIds) {
        // 一次性批量查询所有意图的绑定关系
        List<FlowIntention> existingBindings = flowIntentionDAO.queryByIntentionIds(accountId, intentionIds);

        for (FlowIntention binding : existingBindings) {
            Long bindFlowId = binding.getFlowId();

            // 关键：只有当意图被其他流程绑定时才报错
            if (bindFlowId != null && !bindFlowId.equals(currentFlowId)) {
                log.warn("意图冲突检查失败: intentionId={}, currentFlowId={}, conflictFlowId={}",
                        binding.getIntentionId(), currentFlowId, bindFlowId);
                throw BizException.of(BizExceptionCode.PARAM_ERROR);
            }
        }
    }

    /**
     * 获取意图名称
     */
    private String getIntentionName(String accountId, Long intentionId) {
        try {
            Intention intention = intentionService.findOneById(intentionId, accountId);
            return intention != null ? intention.getName() : "未知意图-" + intentionId;
        } catch (Exception e) {
            log.warn("获取意图名称失败，intentionId: {}, error: {}", intentionId, e.getMessage());
            return "意图-" + intentionId;
        }
    }
}
