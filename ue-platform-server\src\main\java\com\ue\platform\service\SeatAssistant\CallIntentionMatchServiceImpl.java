package com.ue.platform.service.seatAssistant;

import com.ue.platform.domain.dto.seatAssistant.CallExecuteContext;
import com.ue.platform.domain.dto.seatAssistant.CallIntentionMatchDTO;
import com.ue.platform.domain.entity.processNavigation.Flow;
import com.ue.platform.domain.entity.processNavigation.FlowIntention;
import com.ue.platform.domain.entity.processNavigation.Intention;
import com.ue.platform.enums.CallSessionRuleEnum;
import com.ue.platform.dao.processNavigation.FlowDAO;
import com.ue.platform.dao.processNavigation.FlowIntentionDAO;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 通话意图匹配服务实现（简化版，无模糊匹配）
 */
@Service
@Slf4j
public class CallIntentionMatchServiceImpl implements CallIntentionMatchService {

    @Autowired
    private IntentionService intentionService;
    
    @Autowired
    private FlowIntentionDAO flowIntentionDAO;
    
    @Autowired
    private FlowDAO flowDAO;

    @Override
    public CallIntentionMatchDTO matchIntention(CallExecuteContext context) {
        String question = context.getQuestion();
        String accountId = context.getAccountId();
        
        log.info("开始通话意图匹配，accountId: {}, question: {}", accountId, question);
        
        // 1. 查询所有启用的意图配置
        List<Intention> intentionList = intentionService.findEnabledIntentions(accountId);
        if (CollectionUtils.isEmpty(intentionList)) {
            log.info("未找到启用的意图配置，accountId: {}", accountId);
            return CallIntentionMatchDTO.fail();
        }
        
        // 2. 获取NLP意图识别结果（简化版，暂时从context获取）
        String nlpIntention = context.getIntention();

        // 3. 批量查询所有意图的流程绑定关系
        List<Long> intentionIds = intentionList.stream().map(Intention::get_id).collect(Collectors.toList());
        List<FlowIntention> allFlowIntentions = flowIntentionDAO.queryByIntentionIds(accountId, intentionIds);
        Map<Long, FlowIntention> intentionFlowMap = allFlowIntentions.stream()
                .collect(Collectors.toMap(FlowIntention::getIntentionId, Function.identity()));

        // 4. 构建流程ID到流程对象的映射（按需查询）
        Map<Long, Flow> flowMap = new HashMap<>();

        // 5. 遍历意图配置进行匹配
        for (Intention intention : intentionList) {
            // 检查意图绑定的流程是否启用
            FlowIntention flowIntention = intentionFlowMap.get(intention.get_id());
            if (flowIntention == null) {
                continue;
            }

            // 按需查询流程信息
            Flow flow = flowMap.get(flowIntention.getFlowId());
            if (flow == null) {
                flow = flowDAO.findById(flowIntention.getFlowId(), accountId);
                if (flow != null) {
                    flowMap.put(flow.get_id(), flow);
                }
            }

            if (flow == null || !Optional.ofNullable(flow.getEnable()).orElse(false)) {
                continue; // 流程未启用，跳过
            }
            
            // 流程名称完全匹配（最高优先级）
            if (flow.getName().equalsIgnoreCase(question)) {
                log.info("流程名称完全匹配成功，flowId: {}, flowName: {}", flow.get_id(), flow.getName());
                return CallIntentionMatchDTO.success(flow.get_id(), flow, intention.get_id(), intention.getName());
            }
            
            // 执行匹配规则
            CallSessionRuleEnum matchResult = matchRule(context, intention, question, nlpIntention);
            if (matchResult == CallSessionRuleEnum.SUCCESS) {
                log.info("意图匹配成功，intentionId: {}, flowId: {}", intention.get_id(), flow.get_id());
                return CallIntentionMatchDTO.success(flow.get_id(), flow, intention.get_id(), intention.getName());
            }
        }
        
        log.info("意图匹配失败，未找到匹配的流程，accountId: {}, question: {}", accountId, question);
        return CallIntentionMatchDTO.fail();
    }
    
    /**
     * 执行匹配规则
     */
    private CallSessionRuleEnum matchRule(CallExecuteContext context, Intention intention, 
                                         String question, String nlpIntention) {
        
        // 1. 关键词匹配（优先级最高）
        List<String> keywordList = intention.getKeywordList();
        if (!CollectionUtils.isEmpty(keywordList)) {
            for (String keyword : keywordList) {
                if (question.contains(keyword)) {
                    log.info("关键词匹配成功，keyword: {}, question: {}", keyword, question);
                    return CallSessionRuleEnum.SUCCESS;
                }
            }
        }
        
        // 2. 正则表达式匹配
        List<String> regexList = intention.getRegexList();
        if (!CollectionUtils.isEmpty(regexList)) {
            for (String regex : regexList) {
                try {
                    Pattern pattern = Pattern.compile(regex);
                    if (pattern.matcher(question).find()) {
                        log.info("正则匹配成功，regex: {}, question: {}", regex, question);
                        return CallSessionRuleEnum.SUCCESS;
                    }
                } catch (Exception e) {
                    log.warn("正则表达式编译失败，regex: {}, error: {}", regex, e.getMessage());
                }
            }
        }
        
        // 3. NLP意图匹配
        boolean intentionSwitch = Optional.ofNullable(intention.getIntentionSwitch()).orElse(false);
        if (intentionSwitch && StringUtils.hasText(nlpIntention)) {
            List<String> intentionList = intention.getIntentionList();
            if (!CollectionUtils.isEmpty(intentionList)) {
                for (String configIntention : intentionList) {
                    if (nlpIntention.contains(configIntention)) {
                        log.info("NLP意图匹配成功，nlpIntention: {}, configIntention: {}", nlpIntention, configIntention);
                        return CallSessionRuleEnum.SUCCESS;
                    }
                }
            }
        }
        
        return CallSessionRuleEnum.FAIL;
    }
}
